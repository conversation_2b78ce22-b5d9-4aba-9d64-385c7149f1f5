import {
	ImportWorkflowFromUrlDto,
	ManualRunQueryDto,
	TransferWorkflowBodyDto,
} from '@n8n/api-types';
import { Logger } from '@n8n/backend-common';
import {
	Body,
	Delete,
	Get,
	Licensed,
	Param,
	Patch,
	Post,
	ProjectScope,
	Put,
	Query,
	RestController,
} from '@n8n/decorators';
// eslint-disable-next-line n8n-local-rules/misplaced-n8n-typeorm-import

import { GalaxyService } from '../galaxy/galaxy-permission.service';

@RestController('/galaxy')
export class WorkflowsController {
	constructor(
		private readonly logger: Logger,
		private readonly galaxyService: GalaxyService,
	) {}

	@Get('/refProject')
	async getAllRef() {}
}
