import { logger } from '@n8n/backend-common';
import { GlobalConfig } from '@n8n/config';
import { GalaxyPermission, GalaxyPermissionRepository } from '@n8n/db';
import { Service } from '@n8n/di';

import axios from 'axios';

interface galaxyProject {
	projectId: string;
	projectName: string;
}

interface ApiResponseAny {
	code: number;
	message: string;
	result: galaxyUserProject;
}

interface galaxyUserProject {
	uid: string;
	Name: string;
	Email: string;
	projects: galaxyProjectRole[];
}

interface galaxyProjectRole {
	pname: string;
	display: string;
	roles: number[];
}

@Service()
export class GalaxyService {
	constructor(
		private readonly logger: logger,
		private readonly galaxyPermissionRepository: GalaxyPermissionRepository,
		private readonly globalConfig: GlobalConfig,
	) {}

	private apiClient = axios.create({
		baseURL: this.globalConfig.galaxy.prjmHost, // 设置你的基础 URL (host 和路径前缀)
		timeout: 100000,
		headers: {
			ContentType: 'application/json',
		},
	});

	async addGalaxyPermitRecord(gPermit: Omit<GalaxyPermission, 'id'>) {
		return await this.galaxyPermissionRepository.addGalaxyPermitRecord(gPermit);
	}

	async deleteGalaxyPermitRecord(workflowID: string) {
		return await this.galaxyPermissionRepository.deleteGalaxyPermitRecord(workflowID);
	}

	async getRefProject(uid: string): Promise<galaxyProject[]> {
		const userProject = await this.apiClient.get<ApiResponseAny>('/pm/v1/prj/uinfo', {
			params: {
				uid,
			},
		});
		if userProject.code != 0 {
			this.logger.
		}
		return await this.galaxyPermissionRepository.find({ where: { res_id: uid } });
	}
}
